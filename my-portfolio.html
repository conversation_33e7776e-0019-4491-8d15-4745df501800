<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> - Backend Developer</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#007AFF',
                        secondary: '#bd93f9'
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px',
                        'button': '8px'
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif']
                    }
                }
            }
    </script>
    <style>
        :where([class^="ri-"])::before {
            content: "\f3c2";
        }

        body {
            font-family: 'Inter', sans-serif;
        }

        .glass {
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .hero-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .smooth-scroll {
            scroll-behavior: smooth;
        }

        .project-card:hover {
            transform: translateY(-8px);
            transition: all 0.3s ease;
        }

        .achievement-card {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .skill-item {
            transition: all 0.3s ease;
        }

        .skill-item:hover {
            transform: scale(1.05);
            background: rgba(0, 122, 255, 0.1);
        }

        .floating {
            animation: floating 6s ease-in-out infinite;
        }

        @keyframes floating {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-20px);
            }
        }

        .animate-float {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0) rotate(0);
            }

            25% {
                transform: translateY(-10px) rotate(2deg);
            }

            50% {
                transform: translateY(0) rotate(0);
            }

            75% {
                transform: translateY(10px) rotate(-2deg);
            }
        }

        .gradient-text {
            background: linear-gradient(135deg, #007AFF, #5856D6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .certificate-card {
            transition: all 0.4s ease;
            cursor: pointer;
        }

        .certificate-card:hover {
            transform: scale(1.05) rotateY(5deg);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .counter {
            font-variant-numeric: tabular-nums;
        }

        /* Chat Widget Styles */
        .chat-widget {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }

        .chat-toggle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #007AFF, #5856D6);
            color: white;
            border: none;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(0, 122, 255, 0.3);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .chat-toggle:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 25px rgba(0, 122, 255, 0.4);
        }

        .chat-window {
            position: absolute;
            bottom: 80px;
            right: 0;
            width: 350px;
            height: 500px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            display: none;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #007AFF, #5856D6);
            color: white;
            padding: 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .chat-messages {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .message {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }

        .message.user {
            background: #007AFF;
            color: white;
            align-self: flex-end;
            border-bottom-right-radius: 4px;
        }

        .message.bot {
            background: #f1f3f4;
            color: #333;
            align-self: flex-start;
            border-bottom-left-radius: 4px;
        }

        .chat-input-container {
            padding: 16px;
            border-top: 1px solid #e5e7eb;
            display: flex;
            gap: 8px;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #e5e7eb;
            border-radius: 20px;
            outline: none;
            font-size: 14px;
        }

        .chat-input:focus {
            border-color: #007AFF;
        }

        .chat-send {
            background: #007AFF;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s ease;
        }

        .chat-send:hover {
            background: #0056b3;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            gap: 4px;
            padding: 12px 16px;
            background: #f1f3f4;
            border-radius: 18px;
            align-self: flex-start;
            border-bottom-left-radius: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #999;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) {
            animation-delay: -0.32s;
        }

        .typing-dot:nth-child(2) {
            animation-delay: -0.16s;
        }

        @keyframes typing {

            0%,
            80%,
            100% {
                transform: scale(0);
            }

            40% {
                transform: scale(1);
            }
        }
    </style>
</head>

<body class="bg-white text-gray-900 smooth-scroll">
    <!-- Navigation -->
    <nav class="fixed top-0 left-0 right-0 z-50 bg-[#282a36] shadow-lg border-b border-[#44475a]">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <div class="font-bold text-3xl text-[#f8f8f2] font-['Pacifico']">Emmanuel</div>
                <div class="hidden md:flex space-x-8">
                    <a href="#home" class="text-[#f8f8f2] hover:text-[#bd93f9] transition-colors">Home</a>
                    <a href="#projects" class="text-[#f8f8f2] hover:text-[#bd93f9] transition-colors">Projects</a>
                    <a href="#achievements"
                        class="text-[#f8f8f2] hover:text-[#bd93f9] transition-colors">Achievements</a>
                    <a href="#skills" class="text-[#f8f8f2] hover:text-[#bd93f9] transition-colors">Skills</a>
                    <a href="#about" class="text-[#f8f8f2] hover:text-[#bd93f9] transition-colors">About</a>
                    <a href="#contact" class="text-[#f8f8f2] hover:text-[#bd93f9] transition-colors">Contact</a>
                </div>
                <button id="mobile-menu-btn" class="md:hidden w-8 h-8 flex items-center justify-center">
                    <i class="ri-menu-line text-xl"></i>
                </button>
            </div>
        </div>
        <!-- Mobile Menu -->
        <div id="mobile-menu" class="hidden md:hidden bg-white border-t border-gray-200">
            <div class="px-4 py-2 space-y-2">
                <a href="#home" class="block py-2 text-gray-700 hover:text-primary">Home</a>
                <a href="#projects" class="block py-2 text-gray-700 hover:text-primary">Projects</a>
                <a href="#achievements" class="block py-2 text-gray-700 hover:text-primary">Achievements</a>
                <a href="#skills" class="block py-2 text-gray-700 hover:text-primary">Skills</a>
                <a href="#about" class="block py-2 text-gray-700 hover:text-primary">About</a>
                <a href="#contact" class="block py-2 text-gray-700 hover:text-primary">Contact</a>
            </div>
        </div>
    </nav>
    <!-- Hero Section -->
    <section id="home" class="min-h-screen flex items-center relative overflow-hidden">
        <div class="absolute inset-0 bg-[#282a36]"></div>
        <div class="absolute inset-0 bg-blend-overlay"
            style="background-image: url('https://readdy.ai/api/search-image?query=Dark%20cyberpunk%20themed%20workspace%20with%20neon%20purple%20and%20blue%20accents%2C%20floating%20holographic%20code%20fragments%2C%20abstract%20digital%20patterns%2C%20dramatic%20lighting%20with%20deep%20shadows%20and%20vibrant%20accent%20lights%2C%20mysterious%20and%20professional%20dracula%20theme%20atmosphere&width=1920&height=1080&seq=hero3&orientation=landscape'); background-size: cover; background-position: center; opacity: 0.3;">
        </div>
        <div class="absolute inset-0 bg-gradient-to-r from-[#282a36] via-[#282a36]/80 to-transparent"></div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex items-center justify-between">
            <div class="relative z-10 text-[#f8f8f2] max-w-2xl" data-aos="fade-right">
                <h1 class="text-6xl md:text-7xl font-bold mb-6 leading-tight">
                    Hi, I'm <span class="text-[#bd93f9] font-['Pacifico']">Emmanuel</span>
                </h1>
                <p class="text-2xl md:text-3xl mb-8 text-[#f8f8f2] font-light">
                    Backend Developer & Software Engineer
                </p>
                <p class="text-lg md:text-xl mb-12 text-gray-300 leading-relaxed">
                    Crafting robust, scalable backend solutions with modern technologies.
                    Passionate about clean code and system architecture.
                </p>
                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="#projects"
                        class="bg-[#bd93f9] hover:bg-[#bd93f9]/80 text-[#282a36] px-8 py-4 !rounded-button font-medium transition-all duration-300 whitespace-nowrap">
                        View My Work
                    </a>
                    <a href="#contact"
                        class="border-2 border-[#bd93f9] text-[#bd93f9] hover:bg-[#bd93f9] hover:text-[#282a36] px-8 py-4 !rounded-button font-medium transition-all duration-300 whitespace-nowrap">
                        Get In Touch
                    </a>
                </div>
            </div>
            <div class="relative z-10 hidden lg:block w-[500px] h-[500px]" data-aos="fade-left">
                <div
                    class="absolute right-[30px] w-[400px] h-[400px] rounded-full border-2 border-[#bd93f9] animate-[spin_20s_linear_infinite]">
                </div>
                <div class="absolute right-[30px] w-[400px] h-[400px] rounded-full border-2 border-[#ff79c6] animate-[spin_15s_linear_infinite_reverse]"
                    style="transform: rotate(45deg)"></div>
                <div class="absolute right-[30px] w-[300px] h-[300px] mt-[50px] mr-[50px] animate-float">
                    <img src="https://readdy.ai/api/search-image?query=Anime%20style%20portrait%20of%20a%20young%20professional%20male%20developer%2C%20digital%20art%2C%20cyberpunk%20theme%2C%20neon%20lighting%2C%20detailed%20character%20design%2C%20purple%20and%20blue%20color%20scheme%2C%20dracula%20theme%20inspired&width=600&height=600&seq=hero4&orientation=squarish"
                        alt="Animated Developer"
                        class="w-full h-full object-cover rounded-full border-4 border-[#bd93f9] shadow-lg shadow-[#bd93f9]/20 hover:scale-105 transition-transform duration-300">
                </div>
            </div>
        </div>

        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white">
            <div class="w-6 h-10 border-2 border-white rounded-full flex justify-center">
                <div class="w-1 h-3 bg-white rounded-full mt-2 animate-bounce"></div>
            </div>
        </div>
    </section>
    <!-- Add between sections -->
    <div class="section-divider"></div>
    <!-- About Section -->
    <section id="about" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-2 gap-16 items-center">
                <div data-aos="fade-right">
                    <h2 class="text-4xl md:text-5xl font-bold mb-8 gradient-text">About Me</h2>
                    <p class="text-lg text-gray-600 mb-6 leading-relaxed">
                        I'm a passionate Backend Developer with over 2 years of experience building scalable web
                        applications and APIs.
                        My journey in software development started with a curiosity about how things work behind the
                        scenes,
                        and it has evolved into a deep passion for creating robust, efficient systems.
                    </p>
                    <p class="text-lg text-gray-600 mb-6 leading-relaxed">
                        I specialize in Node.js, Python, and cloud technologies, with a strong focus on clean code,
                        system architecture, and performance optimization. When I'm not coding, you'll find me exploring
                        new technologies, contributing to open-source projects, or mentoring aspiring developers.
                    </p>
                    <p class="text-lg text-gray-600 mb-8 leading-relaxed">
                        I believe in the power of technology to solve real-world problems and am always excited to work
                        on projects that make a positive impact on people's lives.
                    </p>
                    <div class="flex flex-wrap gap-4">
                        <div class="flex items-center gap-2 text-gray-600">
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-map-pin-line text-primary"></i>
                            </div>
                            <span>Lagos, Nigeria</span>
                        </div>
                        <div class="flex items-center gap-2 text-gray-600">
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-graduation-cap-line text-primary"></i>
                            </div>
                            <span>Computer Science Graduate</span>
                        </div>
                        <div class="flex items-center gap-2 text-gray-600">
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-briefcase-line text-primary"></i>
                            </div>
                            <span>2+ Years Experience</span>
                        </div>
                    </div>
                </div>
                <div class="relative" data-aos="fade-left">
                    <div class="w-full max-w-md mx-auto">
                        <div class="relative">
                            <div class="w-full h-96 bg-gray-100 rounded-3xl overflow-hidden shadow-2xl">
                                <img src="https://readdy.ai/api/search-image?query=Professional%20African%20male%20software%20developer%20portrait%2C%20confident%20smile%2C%20modern%20office%20background%2C%20clean%20business%20casual%20attire%2C%20professional%20headshot%2C%20tech%20industry%20professional&width=400&height=500&seq=profile1&orientation=portrait"
                                    alt="Emmanuel Makanjuola" class="w-full h-full object-cover object-top">
                            </div>
                            <div
                                class="absolute -bottom-6 -right-6 w-24 h-24 bg-primary rounded-full flex items-center justify-center floating">
                                <i class="ri-code-s-slash-line text-white text-2xl"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Add between sections -->
    <div class="section-divider"></div>
    <!-- Projects Section -->
    <section id="projects" class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16" data-aos="fade-up">
                <h2 class="text-4xl md:text-5xl font-bold mb-6 gradient-text">Featured Projects</h2>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                    A showcase of my recent work in backend development and full-stack applications
                </p>
            </div>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Project 1 -->
                <div class="project-card bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300"
                    data-aos="fade-up" data-aos-delay="100">
                    <div class="w-full h-48 bg-gray-100 rounded-xl mb-6 overflow-hidden">
                        <img src="https://readdy.ai/api/search-image?query=Modern%20e-commerce%20API%20dashboard%20interface%2C%20clean%20design%20with%20data%20visualization%2C%20charts%20and%20graphs%2C%20professional%20backend%20system%2C%20blue%20and%20white%20color%20scheme%2C%20minimalist%20tech%20aesthetic&width=400&height=300&seq=project1&orientation=landscape"
                            alt="E-commerce API" class="w-full h-full object-cover object-top">
                    </div>
                    <h3 class="text-2xl font-bold mb-4">E-commerce REST API</h3>
                    <p class="text-gray-600 mb-6 leading-relaxed">
                        Comprehensive e-commerce backend with user authentication, product management, order processing,
                        and payment integration using Node.js and MongoDB.
                    </p>
                    <div class="flex flex-wrap gap-2 mb-6">
                        <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">Node.js</span>
                        <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">MongoDB</span>
                        <span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm">Express</span>
                        <span class="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm">JWT</span>
                    </div>
                    <div class="flex gap-4">
                        <a href="#" class="flex items-center gap-2 text-primary hover:text-blue-600 transition-colors">
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-github-line"></i>
                            </div>
                            GitHub
                        </a>
                        <a href="#" class="flex items-center gap-2 text-primary hover:text-blue-600 transition-colors">
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-external-link-line"></i>
                            </div>
                            Live Demo
                        </a>
                    </div>
                </div>
                <!-- Project 2 -->
                <div class="project-card bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300"
                    data-aos="fade-up" data-aos-delay="200">
                    <div class="w-full h-48 bg-gray-100 rounded-xl mb-6 overflow-hidden">
                        <img src="https://readdy.ai/api/search-image?query=Real-time%20chat%20application%20interface%2C%20modern%20messaging%20system%2C%20WebSocket%20connections%20visualization%2C%20clean%20UI%20with%20message%20bubbles%2C%20professional%20communication%20platform%20design&width=400&height=300&seq=project2&orientation=landscape"
                            alt="Real-time Chat App" class="w-full h-full object-cover object-top">
                    </div>
                    <h3 class="text-2xl font-bold mb-4">Real-time Chat Application</h3>
                    <p class="text-gray-600 mb-6 leading-relaxed">
                        WebSocket-based chat application with real-time messaging, user presence, file sharing, and room
                        management built with Socket.io and React.
                    </p>
                    <div class="flex flex-wrap gap-2 mb-6">
                        <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">Socket.io</span>
                        <span class="bg-cyan-100 text-cyan-800 px-3 py-1 rounded-full text-sm">React</span>
                        <span class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm">Redis</span>
                        <span class="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm">PostgreSQL</span>
                    </div>
                    <div class="flex gap-4">
                        <a href="#" class="flex items-center gap-2 text-primary hover:text-blue-600 transition-colors">
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-github-line"></i>
                            </div>
                            GitHub
                        </a>
                        <a href="#" class="flex items-center gap-2 text-primary hover:text-blue-600 transition-colors">
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-external-link-line"></i>
                            </div>
                            Live Demo
                        </a>
                    </div>
                </div>
                <!-- Project 3 -->
                <div class="project-card bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300"
                    data-aos="fade-up" data-aos-delay="300">
                    <div class="w-full h-48 bg-gray-100 rounded-xl mb-6 overflow-hidden">
                        <img src="https://readdy.ai/api/search-image?query=Task%20management%20system%20dashboard%2C%20project%20tracking%20interface%2C%20Kanban%20board%20layout%2C%20productivity%20app%20design%2C%20organized%20workflow%20visualization%2C%20modern%20project%20management%20tool&width=400&height=300&seq=project3&orientation=landscape"
                            alt="Task Management API" class="w-full h-full object-cover object-top">
                    </div>
                    <h3 class="text-2xl font-bold mb-4">Task Management System</h3>
                    <p class="text-gray-600 mb-6 leading-relaxed">
                        Comprehensive project management API with team collaboration, task tracking, deadline
                        management, and reporting features using Python Django.
                    </p>
                    <div class="flex flex-wrap gap-2 mb-6">
                        <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">Django</span>
                        <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">Python</span>
                        <span class="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm">PostgreSQL</span>
                        <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm">Celery</span>
                    </div>
                    <div class="flex gap-4">
                        <a href="#" class="flex items-center gap-2 text-primary hover:text-blue-600 transition-colors">
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-github-line"></i>
                            </div>
                            GitHub
                        </a>
                        <a href="#" class="flex items-center gap-2 text-primary hover:text-blue-600 transition-colors">
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-external-link-line"></i>
                            </div>
                            Live Demo
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Add between sections -->
    <div class="section-divider"></div>
    <!-- Achievements Section -->
    <section id="achievements" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16" data-aos="fade-up">
                <h2 class="text-4xl md:text-5xl font-bold mb-6 gradient-text">Achievements & Recognition</h2>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                    Celebrating milestones and recognitions in my development journey
                </p>
            </div>
            <!-- Achievement Stats -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16" data-aos="fade-up" data-aos-delay="100">
                <div class="text-center">
                    <div class="text-4xl md:text-5xl font-bold gradient-text counter" data-target="1">0</div>
                    <p class="text-gray-600 mt-2">Hackathon Won</p>
                </div>
                <div class="text-center">
                    <div class="text-4xl md:text-5xl font-bold gradient-text counter" data-target="3">0</div>
                    <p class="text-gray-600 mt-2">Certificates</p>
                </div>
                <div class="text-center">
                    <div class="text-4xl md:text-5xl font-bold gradient-text counter" data-target="15">0</div>
                    <p class="text-gray-600 mt-2">Projects Completed</p>
                </div>
                <div class="text-center">
                    <div class="text-4xl md:text-5xl font-bold gradient-text counter" data-target="2">0</div>
                    <p class="text-gray-600 mt-2">Years Experience</p>
                </div>
            </div>
            <!-- Hackathon Winner Card -->
            <div class="mb-16" data-aos="fade-up" data-aos-delay="200">
                <div class="achievement-card rounded-3xl p-8 md:p-12 relative overflow-hidden">
                    <div class="absolute top-4 right-4">
                        <div class="w-16 h-16 bg-yellow-400 rounded-full flex items-center justify-center">
                            <i class="ri-trophy-fill text-2xl text-yellow-800"></i>
                        </div>
                    </div>
                    <div class="grid md:grid-cols-2 gap-8 items-center">
                        <div>
                            <div class="flex items-center gap-3 mb-4">
                                <div class="w-12 h-12 bg-primary rounded-xl flex items-center justify-center">
                                    <i class="ri-code-s-slash-line text-white text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="text-2xl md:text-3xl font-bold">TechCrunch Hackathon 2024</h3>
                                    <p class="text-primary font-semibold">First Place Winner</p>
                                </div>
                            </div>
                            <p class="text-gray-600 mb-6 leading-relaxed">
                                Won first place in the annual TechCrunch Hackathon with an innovative fintech solution
                                that streamlines micro-lending processes using blockchain technology and AI-driven risk
                                assessment.
                            </p>
                            <div class="flex flex-wrap gap-2 mb-4">
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">Blockchain</span>
                                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">AI/ML</span>
                                <span
                                    class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm">Node.js</span>
                                <span class="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm">React</span>
                            </div>
                            <p class="text-sm text-gray-500">March 2024 • San Francisco, CA</p>
                        </div>
                        <div class="w-full h-64 bg-gray-100 rounded-2xl overflow-hidden">
                            <img src="https://readdy.ai/api/search-image?query=Hackathon%20winner%20celebration%2C%20developers%20with%20trophy%2C%20tech%20competition%20award%20ceremony%2C%20coding%20event%20victory%2C%20professional%20achievement%20moment%2C%20modern%20tech%20conference%20setting&width=500&height=400&seq=hackathon1&orientation=landscape"
                                alt="Hackathon Winner" class="w-full h-full object-cover object-top">
                        </div>
                    </div>
                </div>
            </div>
            <!-- Certificates Grid -->
            <div class="grid md:grid-cols-3 gap-8" data-aos="fade-up" data-aos-delay="300">
                <!-- Certificate 1 -->
                <div class="certificate-card bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
                    <div class="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mb-4">
                        <i class="ri-amazon-line text-2xl text-blue-600"></i>
                    </div>
                    <h4 class="text-xl font-bold mb-2">AWS Certified Developer</h4>
                    <p class="text-gray-600 mb-4">Amazon Web Services</p>
                    <p class="text-sm text-gray-500 mb-4">
                        Certified in developing and maintaining applications on AWS platform with expertise in
                        serverless architecture and cloud services.
                    </p>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-500">Issued: Jan 2024</span>
                        <div class="w-6 h-6 flex items-center justify-center">
                            <i class="ri-verified-badge-fill text-green-500"></i>
                        </div>
                    </div>
                </div>
                <!-- Certificate 2 -->
                <div class="certificate-card bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
                    <div class="w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center mb-4">
                        <i class="ri-google-line text-2xl text-green-600"></i>
                    </div>
                    <h4 class="text-xl font-bold mb-2">Google Cloud Professional</h4>
                    <p class="text-gray-600 mb-4">Google Cloud Platform</p>
                    <p class="text-sm text-gray-500 mb-4">
                        Professional certification in designing, building, and managing robust, secure, scalable, and
                        dynamic solutions on GCP.
                    </p>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-500">Issued: Nov 2023</span>
                        <div class="w-6 h-6 flex items-center justify-center">
                            <i class="ri-verified-badge-fill text-green-500"></i>
                        </div>
                    </div>
                </div>
                <!-- Certificate 3 -->
                <div class="certificate-card bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
                    <div class="w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center mb-4">
                        <i class="ri-microsoft-line text-2xl text-purple-600"></i>
                    </div>
                    <h4 class="text-xl font-bold mb-2">Azure Fundamentals</h4>
                    <p class="text-gray-600 mb-4">Microsoft Azure</p>
                    <p class="text-sm text-gray-500 mb-4">
                        Foundational knowledge of cloud services and how those services are provided with Microsoft
                        Azure platform.
                    </p>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-500">Issued: Sep 2023</span>
                        <div class="w-6 h-6 flex items-center justify-center">
                            <i class="ri-verified-badge-fill text-green-500"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Add between sections -->
    <div class="section-divider"></div>
    <!-- Skills Section -->
    <section id="skills" class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16" data-aos="fade-up">
                <h2 class="text-4xl md:text-5xl font-bold mb-6 gradient-text">Technical Skills</h2>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                    A comprehensive toolkit for building modern, scalable applications
                </p>
            </div>
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Backend Technologies -->
                <div data-aos="fade-up" data-aos-delay="100">
                    <h3 class="text-xl font-bold mb-6 text-center">Backend</h3>
                    <div class="space-y-4">
                        <div
                            class="skill-item bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex items-center gap-3">
                            <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                <i class="ri-nodejs-line text-green-600"></i>
                            </div>
                            <span class="font-medium">Node.js</span>
                        </div>
                        <div
                            class="skill-item bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex items-center gap-3">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="ri-code-line text-blue-600"></i>
                            </div>
                            <span class="font-medium">Python</span>
                        </div>
                        <div
                            class="skill-item bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex items-center gap-3">
                            <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                                <i class="ri-database-line text-red-600"></i>
                            </div>
                            <span class="font-medium">Django</span>
                        </div>
                        <div
                            class="skill-item bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex items-center gap-3">
                            <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                                <i class="ri-server-line text-gray-600"></i>
                            </div>
                            <span class="font-medium">Express.js</span>
                        </div>
                    </div>
                </div>
                <!-- Databases -->
                <div data-aos="fade-up" data-aos-delay="200">
                    <h3 class="text-xl font-bold mb-6 text-center">Databases</h3>
                    <div class="space-y-4">
                        <div
                            class="skill-item bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex items-center gap-3">
                            <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                <i class="ri-database-2-line text-green-600"></i>
                            </div>
                            <span class="font-medium">MongoDB</span>
                        </div>
                        <div
                            class="skill-item bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex items-center gap-3">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="ri-database-line text-blue-600"></i>
                            </div>
                            <span class="font-medium">PostgreSQL</span>
                        </div>
                        <div
                            class="skill-item bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex items-center gap-3">
                            <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                                <i class="ri-database-2-line text-red-600"></i>
                            </div>
                            <span class="font-medium">Redis</span>
                        </div>
                        <div
                            class="skill-item bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex items-center gap-3">
                            <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                                <i class="ri-database-line text-orange-600"></i>
                            </div>
                            <span class="font-medium">MySQL</span>
                        </div>
                    </div>
                </div>
                <!-- Cloud & DevOps -->
                <div data-aos="fade-up" data-aos-delay="300">
                    <h3 class="text-xl font-bold mb-6 text-center">Cloud & DevOps</h3>
                    <div class="space-y-4">
                        <div
                            class="skill-item bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex items-center gap-3">
                            <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                                <i class="ri-amazon-line text-orange-600"></i>
                            </div>
                            <span class="font-medium">AWS</span>
                        </div>
                        <div
                            class="skill-item bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex items-center gap-3">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="ri-cloud-line text-blue-600"></i>
                            </div>
                            <span class="font-medium">Docker</span>
                        </div>
                        <div
                            class="skill-item bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex items-center gap-3">
                            <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                                <i class="ri-git-branch-line text-purple-600"></i>
                            </div>
                            <span class="font-medium">Kubernetes</span>
                        </div>
                        <div
                            class="skill-item bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex items-center gap-3">
                            <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                                <i class="ri-github-line text-gray-600"></i>
                            </div>
                            <span class="font-medium">CI/CD</span>
                        </div>
                    </div>
                </div>
                <!-- Frontend -->
                <div data-aos="fade-up" data-aos-delay="400">
                    <h3 class="text-xl font-bold mb-6 text-center">Frontend</h3>
                    <div class="space-y-4">
                        <div
                            class="skill-item bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex items-center gap-3">
                            <div class="w-10 h-10 bg-cyan-100 rounded-lg flex items-center justify-center">
                                <i class="ri-reactjs-line text-cyan-600"></i>
                            </div>
                            <span class="font-medium">React</span>
                        </div>
                        <div
                            class="skill-item bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex items-center gap-3">
                            <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                                <i class="ri-javascript-line text-yellow-600"></i>
                            </div>
                            <span class="font-medium">JavaScript</span>
                        </div>
                        <div
                            class="skill-item bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex items-center gap-3">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="ri-css3-line text-blue-600"></i>
                            </div>
                            <span class="font-medium">CSS3</span>
                        </div>
                        <div
                            class="skill-item bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex items-center gap-3">
                            <div class="w-10 h-10 bg-cyan-100 rounded-lg flex items-center justify-center">
                                <i class="ri-tailwind-css-line text-cyan-600"></i>
                            </div>
                            <span class="font-medium">Tailwind CSS</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Add between sections -->
    <div class="section-divider"></div>
    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-gray-50">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16" data-aos="fade-up">
                <h2 class="text-4xl md:text-5xl font-bold mb-6 gradient-text">Let's Work Together</h2>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                    Ready to bring your ideas to life? Let's discuss your next project and create something amazing
                    together.
                </p>
            </div>
            <div class="grid md:grid-cols-2 gap-12">
                <!-- Contact Info -->
                <div data-aos="fade-right">
                    <h3 class="text-2xl font-bold mb-8">Get In Touch</h3>
                    <div class="space-y-6">
                        <div class="flex items-center gap-4">
                            <div class="w-12 h-12 bg-primary rounded-xl flex items-center justify-center">
                                <i class="ri-mail-line text-white"></i>
                            </div>
                            <div>
                                <p class="font-medium">Email</p>
                                <a href="mailto:<EMAIL>"
                                    class="text-gray-600 hover:text-primary transition-colors">
                                    <EMAIL>
                                </a>
                            </div>
                        </div>
                        <div class="flex items-center gap-4">
                            <div class="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center">
                                <i class="ri-linkedin-line text-white"></i>
                            </div>
                            <div>
                                <p class="font-medium">LinkedIn</p>
                                <a href="https://linkedin.com/in/emmanuel-makanjuola"
                                    class="text-gray-600 hover:text-primary transition-colors">
                                    linkedin.com/in/emmanuel-makanjuola
                                </a>
                            </div>
                        </div>
                        <div class="flex items-center gap-4">
                            <div class="w-12 h-12 bg-gray-800 rounded-xl flex items-center justify-center">
                                <i class="ri-github-line text-white"></i>
                            </div>
                            <div>
                                <p class="font-medium">GitHub</p>
                                <a href="https://github.com/emmanuel-makanjuola"
                                    class="text-gray-600 hover:text-primary transition-colors">
                                    github.com/emmanuel-makanjuola
                                </a>
                            </div>
                        </div>
                        <div class="flex items-center gap-4">
                            <div class="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center">
                                <i class="ri-phone-line text-white"></i>
                            </div>
                            <div>
                                <p class="font-medium">Phone</p>
                                <a href="tel:+2348123456789" class="text-gray-600 hover:text-primary transition-colors">
                                    +234 ************
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Contact Form -->
                <div data-aos="fade-left">
                    <form id="contact-form" class="space-y-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Name</label>
                            <input type="text" id="name" name="name" required
                                class="w-full px-4 py-3 border-none bg-white rounded-xl shadow-sm focus:ring-2 focus:ring-primary focus:outline-none text-sm">
                        </div>
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                            <input type="email" id="email" name="email" required
                                class="w-full px-4 py-3 border-none bg-white rounded-xl shadow-sm focus:ring-2 focus:ring-primary focus:outline-none text-sm">
                        </div>
                        <div>
                            <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                            <input type="text" id="subject" name="subject" required
                                class="w-full px-4 py-3 border-none bg-white rounded-xl shadow-sm focus:ring-2 focus:ring-primary focus:outline-none text-sm">
                        </div>
                        <div>
                            <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Message</label>
                            <textarea id="message" name="message" rows="5" required
                                class="w-full px-4 py-3 border-none bg-white rounded-xl shadow-sm focus:ring-2 focus:ring-primary focus:outline-none resize-none text-sm"></textarea>
                        </div>
                        <button type="submit"
                            class="w-full bg-primary hover:bg-blue-600 text-white py-4 px-6 !rounded-button font-medium transition-all duration-300 whitespace-nowrap">
                            Send Message
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>
    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h3 class="text-2xl font-bold mb-4 gradient-text">Emmanuel Makanjuola</h3>
                <p class="text-gray-400 mb-8">Backend Developer & Software Engineer</p>
                <div class="flex justify-center space-x-6 mb-8">
                    <a href="https://github.com/emmanuel-makanjuola"
                        class="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-gray-700 transition-colors">
                        <i class="ri-github-line"></i>
                    </a>
                    <a href="https://linkedin.com/in/emmanuel-makanjuola"
                        class="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-gray-700 transition-colors">
                        <i class="ri-linkedin-line"></i>
                    </a>
                    <a href="mailto:<EMAIL>"
                        class="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-gray-700 transition-colors">
                        <i class="ri-mail-line"></i>
                    </a>
                    <a href="https://twitter.com/emmanuel_codes"
                        class="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-gray-700 transition-colors">
                        <i class="ri-twitter-line"></i>
                    </a>
                </div>
                <div class="border-t border-gray-800 pt-8">
                    <p class="text-gray-400 text-sm">
                        © 2024 Emmanuel Makanjuola. All rights reserved. Built with passion and modern web technologies.
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Chat Widget -->
    <div class="chat-widget">
        <button class="chat-toggle" id="chat-toggle">
            <i class="ri-message-3-line"></i>
        </button>
        <div class="chat-window" id="chat-window">
            <div class="chat-header">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                        <i class="ri-robot-line text-sm"></i>
                    </div>
                    <div>
                        <h4 class="font-semibold">Emmanuel's Assistant</h4>
                        <p class="text-xs opacity-80">Ask me anything!</p>
                    </div>
                </div>
                <button class="chat-close" id="chat-close">
                    <i class="ri-close-line"></i>
                </button>
            </div>
            <div class="chat-messages" id="chat-messages">
                <div class="message bot">
                    👋 Hi! I'm Emmanuel's virtual assistant. I can help you learn more about his skills, projects, and
                    experience. What would you like to know?
                </div>
            </div>
            <div class="typing-indicator" id="typing-indicator">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
            <div class="chat-input-container">
                <input type="text" class="chat-input" id="chat-input" placeholder="Type your message..."
                    maxlength="500">
                <button class="chat-send" id="chat-send">
                    <i class="ri-send-plane-line"></i>
                </button>
            </div>
        </div>
    </div>

    <script id="mobile-menu-script">
document.addEventListener('DOMContentLoaded', function () {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileMenu = document.getElementById('mobile-menu');
    mobileMenuBtn.addEventListener('click', function () {
        mobileMenu.classList.toggle('hidden');
    });
    // Close mobile menu when clicking on links
    const mobileLinks = mobileMenu.querySelectorAll('a');
    mobileLinks.forEach(link => {
        link.addEventListener('click', function () {
            mobileMenu.classList.add('hidden');
        });
    });
});
    </script>
    <script id="smooth-scroll-script">
            document.addEventListener('DOMContentLoaded', function () {
                // Smooth scrolling for navigation links
                const navLinks = document.querySelectorAll('a[href^="#"]');
                navLinks.forEach(link => {
                    link.addEventListener('click', function (e) {
                        e.preventDefault();
                        const targetId = this.getAttribute('href');
                        const targetSection = document.querySelector(targetId);
                        if (targetSection) {
                            const offsetTop = targetSection.offsetTop - 80;
                            window.scrollTo({
                                top: offsetTop,
                                behavior: 'smooth'
                            });
                        }
                    });
                });
            });
    </script>
    <script id="counter-animation-script">
            document.addEventListener('DOMContentLoaded', function () {
                const counters = document.querySelectorAll('.counter');
                const animateCounter = (counter) => {
                    const target = parseInt(counter.getAttribute('data-target'));
                    const increment = target / 100;
                    let current = 0;
                    const updateCounter = () => {
                        if (current < target) {
                            current += increment;
                            counter.textContent = Math.ceil(current);
                            requestAnimationFrame(updateCounter);
                        } else {
                            counter.textContent = target;
                        }
                    };
                    updateCounter();
                };
                // Intersection Observer for counter animation
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            animateCounter(entry.target);
                            observer.unobserve(entry.target);
                        }
                    });
                });
                counters.forEach(counter => {
                    observer.observe(counter);
                });
            });
    </script>
    <script id="contact-form-script">
            document.addEventListener('DOMContentLoaded', function () {
                const contactForm = document.getElementById('contact-form');
                contactForm.addEventListener('submit', function (e) {
                    e.preventDefault();
                    // Get form data
                    const formData = new FormData(contactForm);
                    const name = formData.get('name');
                    const email = formData.get('email');
                    const subject = formData.get('subject');
                    const message = formData.get('message');
                    // Simple validation
                    if (!name || !email || !subject || !message) {
                        alert('Please fill in all fields');
                        return;
                    }
                    // Simulate form submission
                    const submitBtn = contactForm.querySelector('button[type="submit"]');
                    const originalText = submitBtn.textContent;
                    submitBtn.textContent = 'Sending...';
                    submitBtn.disabled = true;
                    setTimeout(() => {
                        alert('Thank you for your message! I\'ll get back to you soon.');
                        contactForm.reset();
                        submitBtn.textContent = originalText;
                        submitBtn.disabled = false;
                    }, 2000);
                });
            });
    </script>
    <script id="aos-init-script">
            document.addEventListener('DOMContentLoaded', function () {
                AOS.init({
                    duration: 1000,
                    once: true,
                    offset: 100,
                    easing: 'ease-out-cubic'
                });
            });
    </script>
</body>

</html>